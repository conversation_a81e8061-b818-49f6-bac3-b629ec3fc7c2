"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface CompressedImage {
  id: string;
  originalFile: File;
  compressedBlob: Blob;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  originalUrl: string;
  compressedUrl: string;
  name: string;
  type: string;
  quality: number;
  width?: number;
  height?: number;
}

interface ImagePreviewProps {
  image: CompressedImage;
  locale: string;
  onDownload: () => void;
  onRemove: () => void;
}

export default function ImagePreview({ image, locale, onDownload, onRemove }: ImagePreviewProps) {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const content = {
    en: {
      originalSize: "Original Size",
      compressedSize: "Compressed Size",
      savings: "Savings",
      download: "Download",
      remove: "Remove"
    },
    zh: {
      originalSize: "原始大小",
      compressedSize: "压缩后大小",
      savings: "节省空间",
      download: "下载",
      remove: "移除"
    }
  };

  const t = content[locale as keyof typeof content] || content.en;

  return (
    <Card className="overflow-hidden">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg truncate">{image.name}</CardTitle>
          <Badge variant="secondary">
            {Math.round(image.compressionRatio)}% {t.savings}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Image Preview */}
          <div className="relative h-48 bg-gray-100 rounded-lg overflow-hidden">
            <img
              src={image.compressedUrl}
              alt={image.name}
              className="w-full h-full object-contain"
            />
          </div>

          {/* File Info */}
          <div className="grid grid-cols-3 gap-2 text-sm">
            <div>
              <p className="text-muted-foreground">{t.originalSize}</p>
              <p className="font-medium">{formatFileSize(image.originalSize)}</p>
            </div>
            <div>
              <p className="text-muted-foreground">{t.compressedSize}</p>
              <p className="font-medium">{formatFileSize(image.compressedSize)}</p>
            </div>
            <div>
              <p className="text-muted-foreground">{t.savings}</p>
              <p className="font-medium text-green-600">
                {formatFileSize(image.originalSize - image.compressedSize)}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}