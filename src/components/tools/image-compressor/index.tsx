"use client";

import React, { useState, useCallback, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Upload, Download, RotateCcw, FileImage, CheckCircle, AlertCircle, X, Plus, Settings } from 'lucide-react';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import ImageCompressorCore from '@/lib/image-compressor/core';
import ComparisonView from './comparison';

export interface CompressedImage {
  id: string;
  originalFile: File;
  compressedBlob: Blob;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  originalUrl: string;
  compressedUrl: string;
  name: string;
  type: string;
  quality: number;
  width?: number;
  height?: number;
}

interface ImageCompressorProps {
  locale: string;
}

export default function ImageCompressor({ locale }: ImageCompressorProps) {
  const [images, setImages] = useState<CompressedImage[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [globalQuality, setGlobalQuality] = useState([75]); // Lower default for better compression
  const [aggressiveMode, setAggressiveMode] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [currentProcessingFile, setCurrentProcessingFile] = useState<string>('');
  const compressorRef = useRef<ImageCompressorCore | null>(null);

  // Check if device is mobile
  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Initialize compressor
  React.useEffect(() => {
    compressorRef.current = new ImageCompressorCore();
  }, []);

  const content = {
    en: {
      uploadTitle: "Drop images here or click to browse",
      uploadDescription: "Support JPEG, PNG, GIF, WebP • Max 10MB per file",
      addMore: "Add More Images",
      qualityLabel: "Quality",
      qualityHigh: "High Quality",
      qualityMedium: "Balanced",
      qualityLow: "Small Size",
      adjustQuality: "Adjust Quality",
      recompressing: "Recompressing...",
      aggressiveMode: "Aggressive Compression",
      aggressiveModeDesc: "Reduce image size for visible difference",
      downloadAll: "Download All",
      downloadZip: "Download as ZIP",
      clearAll: "Clear All",
      processing: "Compressing...",
      originalSize: "Original",
      compressedSize: "Compressed",
      savings: "Saved",
      download: "Download",
      remove: "Remove",
      reset: "Reset",
      compressionComplete: "Compression Complete!",
      filesSelected: "files selected",
      totalSavings: "Total space saved",
      dragHere: "Drag images here",
      clickToSelect: "Click to select files",
      compressing: "Compressing images...",
      ready: "Ready to download"
    },
    zh: {
      uploadTitle: "拖拽图片到这里或点击选择文件",
      uploadDescription: "支持 JPEG、PNG、GIF、WebP • 单个文件最大 10MB",
      addMore: "添加更多图片",
      qualityLabel: "质量",
      qualityHigh: "高质量",
      qualityMedium: "平衡",
      qualityLow: "小体积",
      adjustQuality: "调整质量",
      recompressing: "重新压缩中...",
      aggressiveMode: "激进压缩",
      aggressiveModeDesc: "缩小图片尺寸以产生可见差异",
      downloadAll: "下载全部",
      downloadZip: "下载为 ZIP",
      clearAll: "清除全部",
      processing: "压缩中...",
      originalSize: "原始",
      compressedSize: "压缩后",
      savings: "节省",
      download: "下载",
      remove: "移除",
      reset: "重置",
      compressionComplete: "压缩完成！",
      filesSelected: "个文件已选择",
      totalSavings: "总共节省空间",
      dragHere: "拖拽图片到这里",
      clickToSelect: "点击选择文件",
      compressing: "正在压缩图片...",
      ready: "准备下载"
    }
  };

  const t = content[locale as keyof typeof content] || content.en;

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setErrors([]);
    const newErrors: string[] = [];

    const validFiles = acceptedFiles.filter(file => {
      const isValidType = file.type.startsWith('image/');
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB

      if (!isValidType) {
        newErrors.push(`${file.name}: ${locale === 'zh' ? '不支持的文件格式' : 'Unsupported file format'}`);
        return false;
      }
      if (!isValidSize) {
        newErrors.push(`${file.name}: ${locale === 'zh' ? '文件大小超过10MB限制' : 'File size exceeds 10MB limit'}`);
        return false;
      }
      return true;
    });

    if (newErrors.length > 0) {
      setErrors(newErrors);
    }

    if (validFiles.length === 0) return;

    setIsProcessing(true);
    setProcessingProgress(0);

    const newImages: CompressedImage[] = [];

    for (let i = 0; i < validFiles.length; i++) {
      const file = validFiles[i];
      setCurrentProcessingFile(file.name);
      setProcessingProgress(((i + 0.5) / validFiles.length) * 100);

      try {
        if (compressorRef.current) {
          // Use aggressive mode to resize image for visible difference
          const maxDimension = aggressiveMode ? 1200 : undefined;
          const result = await compressorRef.current.compressImage(file, globalQuality[0] / 100, maxDimension);

          const compressedImage: CompressedImage = {
            id: `${Date.now()}-${i}`,
            originalFile: file,
            compressedBlob: result.blob,
            originalSize: file.size,
            compressedSize: result.blob.size,
            compressionRatio: ((file.size - result.blob.size) / file.size) * 100,
            originalUrl: URL.createObjectURL(file),
            compressedUrl: URL.createObjectURL(result.blob),
            name: file.name,
            type: file.type,
            quality: globalQuality[0],
            width: result.width,
            height: result.height
          };

          newImages.push(compressedImage);
        }
      } catch (error) {
        console.error('Error compressing image:', error);
        newErrors.push(`${file.name}: ${locale === 'zh' ? '压缩失败' : 'Compression failed'}`);
      }

      setProcessingProgress(((i + 1) / validFiles.length) * 100);
    }

    setImages(prev => [...prev, ...newImages]);
    setIsProcessing(false);
    setCurrentProcessingFile('');
    setProcessingProgress(100);

    if (newErrors.length > 0) {
      setErrors(prev => [...prev, ...newErrors]);
    }
  }, [globalQuality, locale]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    multiple: true
  });

  const handleDownloadAll = async () => {
    if (images.length === 0) return;

    const zip = new JSZip();

    for (const image of images) {
      const fileName = image.name.replace(/\.[^/.]+$/, '') + '_compressed' + getFileExtension(image.name);
      zip.file(fileName, image.compressedBlob);
    }

    const content = await zip.generateAsync({ type: 'blob' });
    saveAs(content, 'compressed_images.zip');
  };

  const handleDownloadSingle = (image: CompressedImage) => {
    const fileName = image.name.replace(/\.[^/.]+$/, '') + '_compressed' + getFileExtension(image.name);
    saveAs(image.compressedBlob, fileName);
  };

  const handleRemoveImage = (id: string) => {
    setImages(prev => {
      const updated = prev.filter(img => img.id !== id);
      // Clean up URLs
      const imageToRemove = prev.find(img => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.originalUrl);
        URL.revokeObjectURL(imageToRemove.compressedUrl);
      }
      return updated;
    });
  };

  const handleClearAll = () => {
    images.forEach(image => {
      URL.revokeObjectURL(image.originalUrl);
      URL.revokeObjectURL(image.compressedUrl);
    });
    setImages([]);

  };

  const handleRecompressImage = async (imageId: string, newQuality: number) => {
    const imageIndex = images.findIndex(img => img.id === imageId);
    if (imageIndex === -1 || !compressorRef.current) return;

    const image = images[imageIndex];

    try {
      const maxDimension = aggressiveMode ? 1200 : undefined;
      const result = await compressorRef.current.compressImage(image.originalFile, newQuality / 100, maxDimension);

      // Clean up old compressed URL
      URL.revokeObjectURL(image.compressedUrl);

      const updatedImage: CompressedImage = {
        ...image,
        compressedBlob: result.blob,
        compressedSize: result.blob.size,
        compressionRatio: ((image.originalSize - result.blob.size) / image.originalSize) * 100,
        compressedUrl: URL.createObjectURL(result.blob),
        quality: newQuality
      };

      setImages(prev => {
        const newImages = [...prev];
        newImages[imageIndex] = updatedImage;
        return newImages;
      });
    } catch (error) {
      console.error('Error recompressing image:', error);
    }
  };

  const getFileExtension = (filename: string) => {
    return filename.substring(filename.lastIndexOf('.'));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const totalOriginalSize = images.reduce((sum, img) => sum + img.originalSize, 0);
  const totalCompressedSize = images.reduce((sum, img) => sum + img.compressedSize, 0);
  const totalSavings = totalOriginalSize - totalCompressedSize;
  const averageCompressionRatio = images.length > 0 ? (totalSavings / totalOriginalSize) * 100 : 0;

  return (
    <div className="max-w-6xl mx-auto space-y-6 px-4 sm:px-0">
      {/* Quality Control - Always visible at top */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg flex items-center gap-2">
            <Settings className="w-5 h-5 text-blue-600" />
            {t.qualityLabel}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span className="flex items-center gap-1">
                📦 {t.qualityLow}
              </span>
              <span className="flex items-center gap-1">
                ⚖️ {t.qualityMedium}
              </span>
              <span className="flex items-center gap-1">
                ✨ {t.qualityHigh}
              </span>
            </div>
            <Slider
              value={globalQuality}
              onValueChange={setGlobalQuality}
              max={90}
              min={30}
              step={5}
              className="w-full"
            />
            <div className="flex justify-between items-center">
              <div className="text-center">
                <span className="text-2xl font-bold text-blue-600">{globalQuality[0]}%</span>
                <p className="text-sm text-muted-foreground">
                  {globalQuality[0] >= 80 ? t.qualityHigh : globalQuality[0] >= 60 ? t.qualityMedium : t.qualityLow}
                </p>
              </div>
              <div className="text-right space-y-2">
                <div className="text-sm text-muted-foreground">
                  <p>{locale === 'zh' ? '预期压缩率' : 'Expected compression'}</p>
                  <p className="font-semibold text-green-600">
                    ~{Math.round((100 - globalQuality[0]) * (aggressiveMode ? 1.5 : 0.8))}%
                  </p>
                </div>

                {/* Aggressive Mode Toggle */}
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="aggressive-mode"
                    checked={aggressiveMode}
                    onChange={(e) => setAggressiveMode(e.target.checked)}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="aggressive-mode" className="text-xs text-gray-700 cursor-pointer">
                    {t.aggressiveMode}
                  </label>
                </div>
                <p className="text-xs text-gray-500">
                  {t.aggressiveModeDesc}
                </p>

                {images.length > 0 && (
                  <Button
                    size="sm"
                    onClick={() => {
                      images.forEach(image => {
                        handleRecompressImage(image.id, globalQuality[0]);
                      });
                    }}
                    className="mt-2 text-xs"
                    variant="outline"
                  >
                    <RotateCcw className="w-3 h-3 mr-1" />
                    {locale === 'zh' ? '应用到全部' : 'Apply to All'}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Upload Area */}
      <Card className="relative overflow-hidden">
        <CardContent className="p-0">
          <div
            {...getRootProps()}
            className={`relative transition-all duration-200 ${isDragActive
              ? 'bg-blue-50 border-blue-300'
              : 'bg-gradient-to-br from-gray-50 to-gray-100 hover:from-blue-50 hover:to-blue-100'
              } ${isMobile ? 'p-8' : 'p-12'} border-2 border-dashed border-gray-300 hover:border-blue-400 cursor-pointer`}
          >
            <input {...getInputProps()} />
            <div className="text-center">
              <div className={`mx-auto mb-4 ${isMobile ? 'w-12 h-12' : 'w-16 h-16'} text-gray-400`}>
                <Upload className="w-full h-full" />
              </div>
              <h3 className={`font-semibold mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
                {t.uploadTitle}
              </h3>
              <p className="text-muted-foreground text-sm mb-4">
                {t.uploadDescription}
              </p>
              {images.length > 0 && (
                <Button variant="outline" size="sm" className="mt-2">
                  <Plus className="w-4 h-4 mr-2" />
                  {t.addMore}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Messages */}
      {errors.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="w-4 h-4 text-red-600" />
              <span className="font-medium text-red-800">
                {locale === 'zh' ? '处理错误' : 'Processing Errors'}
              </span>
            </div>
            <ul className="text-sm text-red-700 space-y-1">
              {errors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setErrors([])}
              className="mt-3 text-red-700 border-red-300 hover:bg-red-100"
            >
              <X className="w-3 h-3 mr-1" />
              {locale === 'zh' ? '关闭' : 'Dismiss'}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Processing Status */}
      {isProcessing && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="animate-spin">
                <RotateCcw className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <span className="font-medium text-blue-800">{t.compressing}</span>
                {currentProcessingFile && (
                  <p className="text-sm text-blue-600 mt-1">
                    {locale === 'zh' ? '正在处理：' : 'Processing: '}{currentProcessingFile}
                  </p>
                )}
              </div>
            </div>
            <Progress value={processingProgress} className="w-full h-3" />
            <p className="text-sm text-blue-600 mt-2 text-center">
              {Math.round(processingProgress)}%
            </p>
          </CardContent>
        </Card>
      )}

      {/* Results Summary */}
      {images.length > 0 && !isProcessing && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <CheckCircle className="w-6 h-6 text-green-600" />
              <h3 className="text-lg font-semibold text-green-800">{t.compressionComplete}</h3>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">{images.length}</p>
                <p className="text-sm text-green-700">{t.filesSelected}</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">{formatFileSize(totalOriginalSize)}</p>
                <p className="text-sm text-green-700">{t.originalSize}</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">{formatFileSize(totalCompressedSize)}</p>
                <p className="text-sm text-green-700">{t.compressedSize}</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">{Math.round(averageCompressionRatio)}%</p>
                <p className="text-sm text-green-700">{t.savings}</p>
              </div>
            </div>
            <div className="flex gap-3 justify-center">
              <Button onClick={handleDownloadAll} size="lg" className="flex items-center gap-2">
                <Download className="w-4 h-4" />
                {t.downloadZip}
              </Button>
              <Button variant="outline" onClick={handleClearAll} size="lg" className="flex items-center gap-2">
                <RotateCcw className="w-4 h-4" />
                {t.reset}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Explanation Card */}
      {images.length > 0 && !aggressiveMode && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="text-blue-600 mt-1">💡</div>
              <div>
                <h4 className="text-sm font-medium text-blue-800 mb-1">
                  {locale === 'zh' ? '为什么看不出视觉差异？' : 'Why no visual difference?'}
                </h4>
                <p className="text-sm text-blue-700 leading-relaxed">
                  {locale === 'zh'
                    ? '浏览器显示图片时会重新解码，所以压缩后的图片在视觉上看起来和原图一样。但文件大小确实减小了！下载文件可以验证压缩效果。如果想看到明显的视觉差异，请开启"激进压缩"模式。'
                    : 'Browsers decode images when displaying them, so compressed images look identical to originals visually. But the file size is actually reduced! Download the files to verify compression. Enable "Aggressive Compression" to see visible differences.'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Image Results Grid */}
      {images.length > 0 && (
        <div className="space-y-6">
          {images.map((image, index) => (
            <Card
              key={image.id}
              className="overflow-hidden hover:shadow-lg transition-all duration-300 animate-in slide-in-from-bottom-4"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold truncate flex-1 mr-2">{image.name}</h4>
                  <Badge
                    variant="secondary"
                    className={`shrink-0 ${image.compressionRatio > 50
                      ? 'bg-green-100 text-green-800 border-green-200'
                      : image.compressionRatio > 25
                        ? 'bg-blue-100 text-blue-800 border-blue-200'
                        : 'bg-gray-100 text-gray-800 border-gray-200'
                      }`}
                  >
                    -{Math.round(image.compressionRatio)}%
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Quality Adjustment */}
                <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex items-center justify-between mb-3">
                    <label className="text-sm font-medium flex items-center gap-2">
                      <Settings className="w-4 h-4 text-blue-600" />
                      {t.adjustQuality}
                    </label>
                    <span className="text-lg font-bold text-blue-600">{image.quality}%</span>
                  </div>
                  <Slider
                    value={[image.quality]}
                    onValueChange={(value) => handleRecompressImage(image.id, value[0])}
                    max={90}
                    min={30}
                    step={5}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-2">
                    <span className="flex items-center gap-1">
                      📦 {t.qualityLow}
                    </span>
                    <span className="flex items-center gap-1">
                      ⚖️ {t.qualityMedium}
                    </span>
                    <span className="flex items-center gap-1">
                      ✨ {t.qualityHigh}
                    </span>
                  </div>
                </div>

                {/* Comparison View */}
                <ComparisonView
                  image={image}
                  locale={locale}
                  isMobile={isMobile}
                  onQualityChange={(quality) => handleRecompressImage(image.id, quality)}
                />

                {/* Stats Grid */}
                <div className="grid grid-cols-3 gap-3 text-sm">
                  <div className="text-center p-3 bg-orange-50 rounded-lg border border-orange-100">
                    <p className="text-orange-600 text-xs font-medium">{t.originalSize}</p>
                    <p className="font-bold text-orange-800">{formatFileSize(image.originalSize)}</p>
                  </div>
                  <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-100">
                    <p className="text-blue-600 text-xs font-medium">{t.compressedSize}</p>
                    <p className="font-bold text-blue-800">{formatFileSize(image.compressedSize)}</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg border border-green-100">
                    <p className="text-green-600 text-xs font-medium">{t.savings}</p>
                    <p className="font-bold text-green-800">
                      {formatFileSize(image.originalSize - image.compressedSize)}
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-2">
                  <Button
                    onClick={() => handleDownloadSingle(image)}
                    className="flex-1 flex items-center gap-2 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
                    size="lg"
                  >
                    <Download className="w-4 h-4" />
                    {t.download}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleRemoveImage(image.id)}
                    size="lg"
                    className="px-4 hover:bg-red-50 hover:border-red-200 hover:text-red-700"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Empty State */}
      {images.length === 0 && !isProcessing && (
        <Card className="border-dashed">
          <CardContent className="p-12 text-center">
            <FileImage className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-600 mb-2">
              {t.dragHere}
            </h3>
            <p className="text-muted-foreground">
              {t.clickToSelect}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}