"use client";

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { RotateCcw, Maximize2, SplitSquareHorizontal, Eye } from 'lucide-react';

interface CompressedImage {
  id: string;
  originalFile: File;
  compressedBlob: Blob;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  originalUrl: string;
  compressedUrl: string;
  name: string;
  type: string;
  quality: number;
  width?: number;
  height?: number;
}

interface ComparisonViewProps {
  image: CompressedImage;
  locale: string;
  isMobile?: boolean;
  onQualityChange?: (quality: number) => void;
}

export default function ComparisonView({ image, locale, isMobile = false, onQualityChange }: ComparisonViewProps) {
  const [viewMode, setViewMode] = useState<'split' | 'toggle'>('split');
  const [splitPosition, setSplitPosition] = useState([50]);
  const [showOriginal, setShowOriginal] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const content = {
    en: {
      original: "Original",
      compressed: "Compressed",
      splitView: "Split View",
      toggleView: "Toggle View",
      dimensions: "Dimensions",
      adjustQuality: "Adjust Quality",
      aggressiveMode: "Aggressive Mode",
      aggressiveHint: "Resize image for visible difference"
    },
    zh: {
      original: "原图",
      compressed: "压缩后",
      splitView: "分屏对比",
      toggleView: "切换对比",
      dimensions: "尺寸",
      adjustQuality: "调整质量",
      aggressiveMode: "激进模式",
      aggressiveHint: "缩小图片尺寸以产生可见差异"
    }
  };

  const t = content[locale as keyof typeof content] || content.en;

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const handleStart = (clientX: number) => {
    setIsDragging(true);
  };

  const handleMove = (clientX: number) => {
    if (!isDragging || !containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = clientX - rect.left;
    const percentage = Math.max(10, Math.min(90, (x / rect.width) * 100));
    setSplitPosition([percentage]);
  };

  const handleEnd = () => {
    setIsDragging(false);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    handleStart(e.clientX);
    e.preventDefault();
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    handleStart(e.touches[0].clientX);
    e.preventDefault();
  };

  const handleMouseMove = (e: MouseEvent) => {
    handleMove(e.clientX);
  };

  const handleTouchMove = (e: TouchEvent) => {
    handleMove(e.touches[0].clientX);
  };

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleEnd);
      document.addEventListener('touchmove', handleTouchMove);
      document.addEventListener('touchend', handleEnd);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleEnd);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleEnd);
      };
    }
  }, [isDragging]);

  return (
    <div className="space-y-4">
      {/* View Mode Controls */}
      <div className="flex items-center justify-between">
        <div className="flex gap-2">
          <Button
            size="sm"
            variant={viewMode === 'split' ? 'default' : 'outline'}
            onClick={() => setViewMode('split')}
            className="text-xs"
          >
            <SplitSquareHorizontal className="w-3 h-3 mr-1" />
            {t.splitView}
          </Button>
          <Button
            size="sm"
            variant={viewMode === 'toggle' ? 'default' : 'outline'}
            onClick={() => setViewMode('toggle')}
            className="text-xs"
          >
            <Eye className="w-3 h-3 mr-1" />
            {t.toggleView}
          </Button>
        </div>
        
        {/* File size comparison with compression ratio */}
        <div className="text-right">
          <div className="text-xs text-muted-foreground">
            {formatFileSize(image.originalSize)} → {formatFileSize(image.compressedSize)}
          </div>
          <div className={`text-xs font-medium ${
            image.compressionRatio > 30 ? 'text-green-600' : 
            image.compressionRatio > 10 ? 'text-blue-600' : 'text-yellow-600'
          }`}>
            -{Math.round(image.compressionRatio)}% {locale === 'zh' ? '压缩' : 'compressed'}
          </div>
        </div>
      </div>

      {/* Image Comparison */}
      <div className="relative">
        {viewMode === 'split' ? (
          // Split View
          <div 
            ref={containerRef}
            className="relative bg-gray-50 rounded-lg overflow-hidden border select-none mx-auto"
            style={{ 
              height: isMobile ? '300px' : '500px',
              minHeight: '300px',
              maxWidth: isMobile ? '100%' : '600px'
            }}
          >
            {/* Background Images - Full size for proper comparison */}
            <div className="absolute inset-0">
              <img
                src={image.originalUrl}
                alt={`${image.name} - Original`}
                className="w-full h-full object-contain"
              />
            </div>
            <div className="absolute inset-0">
              <img
                src={image.compressedUrl}
                alt={`${image.name} - Compressed`}
                className="w-full h-full object-contain"
                style={{ 
                  clipPath: `polygon(${splitPosition[0]}% 0%, 100% 0%, 100% 100%, ${splitPosition[0]}% 100%)`
                }}
              />
            </div>
            

            
            {/* Labels */}
            <div className="absolute top-2 left-2 z-20">
              <div className="px-3 py-1 bg-orange-500 text-white rounded-full text-xs font-medium shadow-lg">
                {t.original}
              </div>
            </div>
            <div className="absolute top-2 right-2 z-20">
              <div className="px-3 py-1 bg-green-500 text-white rounded-full text-xs font-medium shadow-lg">
                {t.compressed}
              </div>
            </div>
            
            {/* File sizes */}
            <div className="absolute bottom-2 left-2 z-20">
              <div className="px-2 py-1 bg-white/90 text-gray-700 rounded text-xs font-medium">
                {formatFileSize(image.originalSize)}
              </div>
            </div>
            <div className="absolute bottom-2 right-2 z-20">
              <div className="px-2 py-1 bg-white/90 text-gray-700 rounded text-xs font-medium">
                {formatFileSize(image.compressedSize)}
              </div>
            </div>
            
            {/* Split Line */}
            <div 
              className="absolute top-0 bottom-0 w-1 bg-white shadow-lg z-30 cursor-col-resize hover:bg-blue-200 transition-colors"
              style={{ left: `${splitPosition[0]}%`, marginLeft: '-2px' }}
              onMouseDown={handleMouseDown}
              onTouchStart={handleTouchStart}
            >
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-blue-50 transition-colors">
                <SplitSquareHorizontal className="w-4 h-4 text-gray-600" />
              </div>
            </div>
          </div>
        ) : (
          // Toggle View
          <div 
            className="relative bg-gray-50 rounded-lg overflow-hidden border group mx-auto"
            style={{ 
              height: isMobile ? '300px' : '500px',
              minHeight: '300px',
              maxWidth: isMobile ? '100%' : '600px'
            }}
          >
            <img
              src={showOriginal ? image.originalUrl : image.compressedUrl}
              alt={image.name}
              className="w-full h-full object-contain transition-all duration-300"
            />
            
            {/* Toggle Button */}
            <div className={`absolute top-2 right-2 ${isMobile ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'} transition-opacity duration-200`}>
              <Button
                size="sm"
                variant="secondary"
                onClick={() => setShowOriginal(!showOriginal)}
                className="text-xs shadow-lg"
              >
                <RotateCcw className="w-3 h-3 mr-1" />
                {showOriginal ? t.compressed : t.original}
              </Button>
            </div>

            {/* Status Indicator */}
            <div className="absolute bottom-2 left-2">
              <div className={`px-2 py-1 rounded text-xs font-medium transition-all duration-300 ${
                showOriginal 
                  ? 'bg-orange-100 text-orange-800 border border-orange-200' 
                  : 'bg-green-100 text-green-800 border border-green-200'
              }`}>
                {showOriginal ? t.original : t.compressed}
              </div>
            </div>

            {/* File Size */}
            <div className="absolute bottom-2 right-2">
              <div className="px-2 py-1 bg-white/90 text-gray-700 rounded text-xs font-medium">
                {formatFileSize(showOriginal ? image.originalSize : image.compressedSize)}
              </div>
            </div>
          </div>
        )}

        {/* Split Position Slider */}
        {viewMode === 'split' && (
          <div className="mt-3 space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                🟠 {t.original}
              </span>
              <span className="text-center font-medium">
                {Math.round(splitPosition[0])}% | {Math.round(100 - splitPosition[0])}%
              </span>
              <span className="flex items-center gap-1">
                🟢 {t.compressed}
              </span>
            </div>
            <Slider
              value={splitPosition}
              onValueChange={setSplitPosition}
              max={90}
              min={10}
              step={1}
              className="w-full"
            />
          </div>
        )}
      </div>

      {/* Image Info */}
      <div className="flex justify-between items-center text-xs text-muted-foreground">
        {image.width && image.height && (
          <span>{image.width} × {image.height}px</span>
        )}
        <span className="font-medium text-green-600">
          -{Math.round(image.compressionRatio)}% {locale === 'zh' ? '压缩' : 'compressed'}
        </span>
      </div>
    </div>
  );
}