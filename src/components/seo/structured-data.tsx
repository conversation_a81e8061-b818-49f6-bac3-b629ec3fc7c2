import Script from 'next/script';

interface StructuredDataProps {
  type?: 'WebApplication' | 'WebSite' | 'SoftwareApplication';
  name?: string;
  description?: string;
  url?: string;
  applicationCategory?: string;
  operatingSystem?: string;
  offers?: {
    price: string;
    priceCurrency: string;
  };
}

export default function StructuredData({
  type = 'WebApplication',
  name = 'Optimize Image',
  description = 'Free online image compression tool powered by AI. Reduce JPEG, PNG, WebP, AVIF file sizes by up to 90% while maintaining perfect quality.',
  url = 'https://optimize-image.com',
  applicationCategory = 'UtilitiesApplication',
  operatingSystem = 'Any',
  offers = {
    price: '0',
    priceCurrency: 'USD'
  }
}: StructuredDataProps) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': type,
    name,
    description,
    url,
    applicationCategory,
    operatingSystem,
    offers: {
      '@type': 'Offer',
      price: offers.price,
      priceCurrency: offers.priceCurrency
    },
    featureList: [
      'AI-powered image compression',
      'Support for JPEG, PNG, WebP, AVIF, GIF formats',
      'Up to 90% file size reduction',
      'Batch processing up to 10 images',
      'Privacy-first local processing',
      'No watermarks',
      'Free forever'
    ],
    screenshot: `${url}/imgs/features/1.jpg`,
    author: {
      '@type': 'Organization',
      name: 'Optimize Image',
      url
    },
    provider: {
      '@type': 'Organization',
      name: 'Optimize Image',
      url
    }
  };

  return (
    <Script
      id="structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  );
}