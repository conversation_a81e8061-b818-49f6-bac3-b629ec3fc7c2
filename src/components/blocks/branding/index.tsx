import { Section as SectionType } from "@/types/blocks/section";

export default function Branding({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-12">
      <div className="container">
        <div className="flex flex-col items-center gap-6">
          <h2 className="text-center text-muted-foreground text-sm font-medium">
            {section.title}
          </h2>
          <div className="flex flex-wrap items-center justify-center gap-6 max-w-2xl">
            {section.items?.map((item, idx) => {
              return (
                <div
                  key={idx}
                  className="relative group flex flex-col items-center gap-3 min-w-[80px]"
                >
                  <div
                    className="flex items-center justify-center w-16 h-16 rounded-xl hover:scale-150 transition-all duration-300 cursor-pointer bg-gray-50/80 dark:bg-gray-800/80 hover:bg-gray-100 dark:hover:bg-gray-700 hover:shadow-lg"
                    title={item.description}
                  >
                    <span className="text-3xl select-none">
                      {item.icon}
                    </span>
                  </div>

                  {/* Format name below icon */}
                  {item.title && (
                    <span className="text-sm font-semibold text-center text-gray-800 dark:text-gray-200">
                      {item.title}
                    </span>
                  )}

                  {/* Tooltip */}
                  {item.description && (
                    <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 px-3 py-2 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-20 shadow-lg">
                      {item.description}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900 dark:border-t-gray-100"></div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
