import { Metadata } from "next";
import ImageCompressor from "@/components/tools/image-compressor";
import StructuredData from "@/components/seo/structured-data";
import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'compress' });
  
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://shipany.ai';
  const canonicalUrl = locale === 'en' ? `${baseUrl}/compress` : `${baseUrl}/${locale}/compress`;
  
  return {
    title: t('metadata.title'),
    description: t('metadata.description'),
    keywords: t('metadata.keywords'),
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'en': `${baseUrl}/compress`,
        'zh': `${baseUrl}/zh/compress`,
      },
    },
    openGraph: {
      title: t('metadata.title'),
      description: t('metadata.description'),
      url: canonicalUrl,
      siteName: 'ShipAny',
      locale: locale,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: t('metadata.title'),
      description: t('metadata.description'),
    },
  };
}

export default async function CompressPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'compress' });

  return (
    <div className="min-h-screen bg-background">
      <StructuredData 
        type="SoftwareApplication"
        name="Free AI Image Compressor"
        description="Compress and optimize images online for free with AI technology. Support for JPEG, PNG, WebP, AVIF, and GIF formats."
        url={`${process.env.NEXT_PUBLIC_WEB_URL || "https://optimize-image.com"}/${locale === 'en' ? '' : locale + '/'}compress`}
        applicationCategory="UtilitiesApplication"
      />
      {/* Hero Section */}
      <section className="py-12 md:py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-8">
            <h1 className="text-3xl md:text-5xl font-bold tracking-tight mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {t('hero.title')}
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground mb-6">
              {t('hero.subtitle')}
            </p>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              {t('hero.description')}
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-w-4xl mx-auto">
            {[
              { text: t('features.formats'), icon: '🖼️' },
              { text: t('features.reduction'), icon: '📉' },
              { text: t('features.batch'), icon: '⚡' },
              { text: t('features.privacy'), icon: '🔒' },
              { text: t('features.preview'), icon: '👁️' },
              { text: t('features.noLimits'), icon: '∞' }
            ].map((feature, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-white/70 backdrop-blur-sm rounded-lg border border-white/50 shadow-sm hover:shadow-md transition-shadow">
                <span className="text-lg">{feature.icon}</span>
                <span className="text-sm font-medium">{feature.text}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Image Compressor Tool */}
      <section className="py-8 md:py-12">
        <div className="container mx-auto px-4">
          <ImageCompressor locale={locale} />
        </div>
      </section>

      {/* How It Works */}
      <section className="py-12 md:py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold text-center mb-12">
              {t('howItWorks.title')}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  step: "1",
                  title: t('howItWorks.steps.upload.title'),
                  description: t('howItWorks.steps.upload.description'),
                  icon: "📤"
                },
                {
                  step: "2", 
                  title: t('howItWorks.steps.compress.title'),
                  description: t('howItWorks.steps.compress.description'),
                  icon: "⚙️"
                },
                {
                  step: "3",
                  title: t('howItWorks.steps.download.title'),
                  description: t('howItWorks.steps.download.description'),
                  icon: "📥"
                }
              ].map((step, index) => (
                <div key={index} className="text-center group">
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-2xl flex items-center justify-center text-2xl font-bold mx-auto shadow-lg group-hover:shadow-xl transition-shadow">
                      {step.icon}
                    </div>
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      {step.step}
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold mb-3">{step.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{step.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}