import { source } from "@/lib/source";
import { createFromSource } from "fumadocs-core/search/server";

// Map locales to supported Orama languages
// https://docs.orama.com/open-source/supported-languages
const languageMap: Record<string, string> = {
  en: "english",
  zh: "english", // Fallback to English for Chinese since "zh" is not supported
  fr: "french",
  ja: "english", // Fallback to English for Japanese
  ko: "english", // Fallback to English for Korean
  ru: "russian",
  it: "italian",
  pt: "portuguese",
  es: "spanish",
  de: "german",
  nl: "dutch",
  vi: "english", // Fallback to English for Vietnamese
};

export const { GET } = createFromSource(source, {
  language: (locale: string) => {
    return languageMap[locale] || "english";
  },
});
