export interface CompressionResult {
  blob: Blob;
  width: number;
  height: number;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
}

export default class ImageCompressorCore {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor() {
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d')!;
  }

  async compressImage(file: File, quality: number = 0.8, maxDimension?: number): Promise<CompressionResult> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        try {
          let { width, height } = { width: img.width, height: img.height };
          
          // Optional resizing for better compression
          if (maxDimension && (width > maxDimension || height > maxDimension)) {
            const ratio = Math.min(maxDimension / width, maxDimension / height);
            width = Math.round(width * ratio);
            height = Math.round(height * ratio);
          }
          
          // Set canvas dimensions
          this.canvas.width = width;
          this.canvas.height = height;

          // Clear canvas and draw image (with potential resizing)
          this.ctx.clearRect(0, 0, width, height);
          this.ctx.drawImage(img, 0, 0, width, height);

          // Determine output format and compression strategy
          let outputFormat = 'image/jpeg';
          let useQuality = quality;
          
          const hasAlpha = this.hasTransparency(img);
          
          if (file.type === 'image/png' && hasAlpha) {
            // PNG with transparency - keep as PNG but try to optimize
            outputFormat = 'image/png';
            useQuality = Math.max(0.6, quality * 0.9); // Slightly more aggressive for PNG
          } else if (file.type === 'image/gif') {
            // GIF - convert based on transparency
            outputFormat = hasAlpha ? 'image/png' : 'image/jpeg';
            useQuality = hasAlpha ? Math.max(0.6, quality * 0.9) : quality;
          } else if (file.type === 'image/webp') {
            // WebP - keep as WebP for best compression
            outputFormat = 'image/webp';
          } else {
            // All other formats (including PNG without transparency) - convert to JPEG
            outputFormat = 'image/jpeg';
          }
          
          // For very high quality settings, be more aggressive to ensure visible compression
          if (quality > 0.9) {
            useQuality = Math.min(0.85, quality);
          }

          // Convert to blob
          this.canvas.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error('Failed to compress image'));
                return;
              }

              const compressionRatio = ((file.size - blob.size) / file.size) * 100;

              resolve({
                blob,
                width,
                height,
                originalSize: file.size,
                compressedSize: blob.size,
                compressionRatio: Math.max(0, compressionRatio)
              });
            },
            outputFormat,
            useQuality
          );
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };

      img.src = URL.createObjectURL(file);
    });
  }

  async resizeAndCompress(
    file: File, 
    maxWidth: number, 
    maxHeight: number, 
    quality: number = 0.8
  ): Promise<CompressionResult> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        try {
          // Calculate new dimensions
          const { width, height } = this.calculateDimensions(
            img.width, 
            img.height, 
            maxWidth, 
            maxHeight
          );

          // Set canvas dimensions
          this.canvas.width = width;
          this.canvas.height = height;

          // Clear canvas and draw resized image
          this.ctx.clearRect(0, 0, width, height);
          this.ctx.drawImage(img, 0, 0, width, height);

          // Determine output format
          let outputFormat = 'image/jpeg';
          if (file.type === 'image/png' && this.hasTransparency(img)) {
            outputFormat = 'image/png';
          } else if (file.type === 'image/webp') {
            outputFormat = 'image/webp';
          }

          // Convert to blob
          this.canvas.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error('Failed to compress image'));
                return;
              }

              const compressionRatio = ((file.size - blob.size) / file.size) * 100;

              resolve({
                blob,
                width,
                height,
                originalSize: file.size,
                compressedSize: blob.size,
                compressionRatio: Math.max(0, compressionRatio)
              });
            },
            outputFormat,
            quality
          );
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };

      img.src = URL.createObjectURL(file);
    });
  }

  private calculateDimensions(
    originalWidth: number, 
    originalHeight: number, 
    maxWidth: number, 
    maxHeight: number
  ): { width: number; height: number } {
    let { width, height } = { width: originalWidth, height: originalHeight };

    // If image is smaller than max dimensions, return original size
    if (width <= maxWidth && height <= maxHeight) {
      return { width, height };
    }

    // Calculate aspect ratio
    const aspectRatio = width / height;

    // Resize based on which dimension is the limiting factor
    if (width > maxWidth) {
      width = maxWidth;
      height = width / aspectRatio;
    }

    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }

    return {
      width: Math.round(width),
      height: Math.round(height)
    };
  }

  private hasTransparency(img: HTMLImageElement): boolean {
    // Create a small canvas to check for transparency
    const testCanvas = document.createElement('canvas');
    const testCtx = testCanvas.getContext('2d')!;
    
    testCanvas.width = Math.min(img.width, 100);
    testCanvas.height = Math.min(img.height, 100);
    
    testCtx.drawImage(img, 0, 0, testCanvas.width, testCanvas.height);
    
    const imageData = testCtx.getImageData(0, 0, testCanvas.width, testCanvas.height);
    const data = imageData.data;
    
    // Check alpha channel (every 4th value)
    for (let i = 3; i < data.length; i += 4) {
      if (data[i] < 255) {
        return true; // Found transparency
      }
    }
    
    return false;
  }

  // Utility method to get image dimensions without loading
  static getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height
        });
        URL.revokeObjectURL(img.src);
      };
      
      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };
      
      img.src = URL.createObjectURL(file);
    });
  }

  // Utility method to convert blob to base64
  static blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  // Clean up resources
  destroy() {
    this.canvas.remove();
  }
}