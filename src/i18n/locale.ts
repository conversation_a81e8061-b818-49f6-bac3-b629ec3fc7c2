import { Pathnames } from "next-intl/routing";

export const locales = [
  "en", // English
  "zh", // Chinese
  "fr", // French
  "ja", // Japanese
  "ko", // Korean
  "ru", // Russian
  "it", // Italian
  "pt", // Portuguese
  "es", // Spanish
  "de", // German
  "nl", // Dutch
  "vi", // Vietnamese
];

export const localeNames: any = {
  en: "English",
  zh: "中文",
  fr: "Français",
  ja: "日本語",
  ko: "한국어",
  ru: "Русский",
  it: "Italiano",
  pt: "Português",
  es: "Español",
  de: "Deutsch",
  nl: "Nederlands",
  vi: "Tiếng Việt",
};

export const defaultLocale = "en";

export const localePrefix = "as-needed";

export const localeDetection =
  process.env.NEXT_PUBLIC_LOCALE_DETECTION === "true";
