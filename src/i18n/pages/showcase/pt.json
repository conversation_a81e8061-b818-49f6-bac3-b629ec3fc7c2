{"showcase": {"name": "showcase", "title": "Startups IA SaaS construídas com ShipAny", "description": "Fácil de usar e rápido para lançar.", "items": [{"title": "ThinkAny", "description": "Mecanismo de Busca IA", "url": "https://thinkany.ai", "target": "_blank", "image": {"src": "/imgs/showcases/7.png"}}, {"title": "HeyBeauty", "description": "Prova Virtual IA", "url": "https://heybeauty.ai", "target": "_blank", "image": {"src": "/imgs/showcases/5.png"}}, {"title": "AI Wallpaper", "description": "Gerador de Papel de Parede IA", "url": "https://aiwallpaper.shop", "target": "_blank", "image": {"src": "/imgs/showcases/1.png"}}, {"title": "AI Cover", "description": "Gerador de Capas IA", "url": "https://aicover.design", "target": "_blank", "image": {"src": "/imgs/showcases/2.png"}}, {"title": "GPTs Works", "description": "Diretório GPTs", "url": "https://gpts.works", "target": "_blank", "image": {"src": "/imgs/showcases/3.png"}}, {"title": "Melod<PERSON>", "description": "Player de Música IA", "url": "https://melodis.co", "target": "_blank", "image": {"src": "/imgs/showcases/4.png"}}, {"title": "<PERSON><PERSON>", "description": "Gerador de Landing Page IA", "url": "https://pagen.so", "target": "_blank", "image": {"src": "/imgs/showcases/6.png"}}, {"title": "SoraFM", "description": "Gerador de Vídeo IA", "url": "https://sorafm.trys.ai", "target": "_blank", "image": {"src": "/imgs/showcases/8.png"}}, {"title": "PodLM", "description": "Gerador de Podcast IA", "url": "https://podlm.ai", "target": "_blank", "image": {"src": "/imgs/showcases/9.png"}}]}}